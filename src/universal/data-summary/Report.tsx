import { SyncOutlined, CloseCircleOutlined } from '@ant-design/icons'
import { useMutation } from '@tanstack/react-query'
import { Button, Modal, Popconfirm, Result } from 'antd'
import { useState } from 'react'

import { type APIResponse, request } from '@/lib/request.ts'

const STATUS_CARD = {
  loading: (
    <Result
      title="上报中..."
      icon={<SyncOutlined spin color="primary" />}
      extra={<Button>取消</Button>}
    />
  ),
  error: (
    <Result
      title="上报失败"
      icon={<CloseCircleOutlined color="red" />}
      extra={<Button>确定</Button>}
    />
  ),
  success: 'success',
}

export const ReportModal = ({ selectedKeys }: { selectedKeys: string[] }) => {
  const [isModalOpen, setIsModalOpen] = useState(false)

  const [reportStatus, setReportStatus] = useState<
    'loading' | 'error' | 'success'
  >('error')
  const [reportMessage, setReportMessage] = useState()

  const handleReport = useMutation({
    mutationFn: async () => {
      setIsModalOpen(true)

      const res = await request<APIResponse<null>>('/approval/pending-mul', {
        method: 'POST',
        body: { node_ids: selectedKeys },
      })

      if (res.code !== 200001) {
        setReportStatus('error')
        setReportMessage(res.message)
        return
      }
    },
  })

  return (
    <>
      <Popconfirm
        title="确认上报所选项？"
        okText="确认"
        cancelText="取消"
        onConfirm={() => setIsModalOpen(true)}
      >
        <Button disabled={selectedKeys.length < 1}>数据上报</Button>
      </Popconfirm>

      <Modal
        title="数据上报"
        width={420}
        height={270}
        open={isModalOpen}
        onCancel={() => setIsModalOpen(false)}
        onOk={() => setIsModalOpen(false)}
        footer={null}
      >
        {STATUS_CARD[reportStatus]}
      </Modal>
    </>
  )
}
